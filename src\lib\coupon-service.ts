// src/lib/coupon-service.ts
import prisma from "./prisma";

export interface CouponValidationResult {
  isValid: boolean;
  coupon?: {
    id: string;
    code: string;
    description: string;
    discountPercentage: number;
  };
  discountAmount?: number;
  finalPrice?: number;
  error?: string;
  isReferralCoupon?: boolean; // Flag to indicate if this is a referral coupon
}

export interface CouponApplicationResult {
  success: boolean;
  couponUsageId?: string;
  discountAmount?: number;
  finalPrice?: number;
  error?: string;
}

export interface CouponCreateData {
  description: string;
  discountPercentage: number;
  maxUses?: number;
  expiresAt?: Date;
}

export interface CouponUpdateData {
  description?: string;
  discountPercentage?: number;
  maxUses?: number;
  expiresAt?: Date;
  isActive?: boolean;
}

class CouponService {
  private couponCache: Map<string, CouponValidationResult & { originalPrice: number }> = new Map();
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

  /**
   * Generate a random coupon code in format XXX-XXX-XXX
   */
  generateCouponCode(): string {
    const generateSegment = () => Math.floor(100 + Math.random() * 900).toString();
    return `${generateSegment()}-${generateSegment()}-${generateSegment()}`;
  }

  /**
   * Create a new coupon
   */
  async createCoupon(data: CouponCreateData): Promise<{ success: boolean; coupon?: {
    id: string;
    code: string;
    description: string;
    discountPercentage: number;
    isActive: boolean;
    maxUses: number | null;
    currentUses: number;
    expiresAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
  }; error?: string }> {
    try {
      // Generate unique coupon code
      let code: string;
      let isUnique = false;
      let attempts = 0;
      const maxAttempts = 10;

      do {
        code = this.generateCouponCode();
        const existing = await prisma.coupon.findUnique({ where: { code } });
        isUnique = !existing;
        attempts++;
      } while (!isUnique && attempts < maxAttempts);

      if (!isUnique) {
        return { success: false, error: "Failed to generate unique coupon code" };
      }

      // Create the coupon
      const coupon = await prisma.coupon.create({
        data: {
          code,
          description: data.description,
          discountPercentage: data.discountPercentage,
          maxUses: data.maxUses,
          expiresAt: data.expiresAt,
        },
      });

      // Clear cache
      this.clearCache();

      return { success: true, coupon };
    } catch (error) {
      console.error("Error creating coupon:", error);
      return { success: false, error: "Failed to create coupon" };
    }
  }

  /**
   * Get all coupons with usage statistics
   */
  async getAllCoupons(): Promise<Array<{
    id: string;
    code: string;
    description: string;
    discountPercentage: number;
    isActive: boolean;
    maxUses: number | null;
    currentUses: number;
    expiresAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
    usageCount: number;
  }>> {
    try {
      const coupons = await prisma.coupon.findMany({
        include: {
          _count: {
            select: {
              couponUsages: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return coupons.map(coupon => ({
        id: coupon.id,
        code: coupon.code,
        description: coupon.description,
        discountPercentage: coupon.discountPercentage,
        isActive: coupon.isActive,
        maxUses: coupon.maxUses,
        currentUses: coupon.currentUses,
        expiresAt: coupon.expiresAt,
        createdAt: coupon.createdAt,
        updatedAt: coupon.updatedAt,
        usageCount: coupon._count?.couponUsages || 0,
      }));
    } catch (error) {
      console.error("Error fetching coupons:", error);
      return [];
    }
  }

  /**
   * Update a coupon
   */
  async updateCoupon(id: string, data: CouponUpdateData): Promise<{ success: boolean; coupon?: {
    id: string;
    code: string;
    description: string;
    discountPercentage: number;
    isActive: boolean;
    maxUses: number | null;
    currentUses: number;
    expiresAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
  }; error?: string }> {
    try {
      const coupon = await prisma.coupon.update({
        where: { id },
        data,
      });

      // Clear cache
      this.clearCache();

      return { success: true, coupon };
    } catch (error) {
      console.error("Error updating coupon:", error);
      return { success: false, error: "Failed to update coupon" };
    }
  }

  /**
   * Delete a coupon
   */
  async deleteCoupon(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      await prisma.coupon.delete({
        where: { id },
      });

      // Clear cache
      this.clearCache();

      return { success: true };
    } catch (error) {
      console.error("Error deleting coupon:", error);
      return { success: false, error: "Failed to delete coupon" };
    }
  }

  /**
   * Validate a coupon code and calculate discount
   */
  async validateCoupon(code: string, userId: string, originalPrice: number): Promise<CouponValidationResult> {
    try {
      console.log("🔍 [COUPON SERVICE] validateCoupon called with:", { code, userId, originalPrice });

      // Validate input parameters
      if (!code || typeof code !== 'string') {
        console.log("❌ [COUPON SERVICE] Invalid code parameter:", code);
        return { isValid: false, error: "Invalid coupon code" };
      }

      if (!userId || typeof userId !== 'string') {
        console.log("❌ [COUPON SERVICE] Invalid userId parameter:", userId);
        return { isValid: false, error: "User authentication required" };
      }

      if (typeof originalPrice !== 'number' || originalPrice < 0) {
        console.log("❌ [COUPON SERVICE] Invalid originalPrice parameter:", originalPrice);
        return { isValid: false, error: "Invalid price amount" };
      }

      // Check cache first
      const cacheKey = `${code}-${userId}`;
      const now = Date.now();

      console.log("🔍 [COUPON SERVICE] Checking cache for key:", cacheKey);

      if (this.cacheExpiry > now && this.couponCache.has(cacheKey)) {
        const cached = this.couponCache.get(cacheKey);
        if (cached && cached.originalPrice === originalPrice) {
          return cached;
        }
      }

      // Find the coupon - check both regular coupons and referral coupons
      console.log("🔍 [COUPON SERVICE] Querying database for coupon:", code);

      // First check regular coupons
      const coupon = await prisma.coupon.findUnique({
        where: { code },
        include: {
          couponUsages: {
            where: { userId },
          },
        },
      });

      console.log("🔍 [COUPON SERVICE] Regular coupon query result:", coupon ? {
        id: coupon.id,
        code: coupon.code,
        isActive: coupon.isActive,
        discountPercentage: coupon.discountPercentage,
        usageCount: coupon.couponUsages.length,
        expiresAt: coupon.expiresAt
      } : "null");

      // If regular coupon found, validate it
      if (coupon) {
        if (!coupon.isActive) {
          return { isValid: false, error: "This coupon is no longer active" };
        }

        // Check expiration
        if (coupon.expiresAt && new Date() > coupon.expiresAt) {
          return { isValid: false, error: "This coupon has expired" };
        }

        // Check if user has already used this coupon
        if (coupon.couponUsages.length > 0) {
          return { isValid: false, error: "You have already used this coupon" };
        }

        // Check max uses
        if (coupon.maxUses && coupon.currentUses >= coupon.maxUses) {
          return { isValid: false, error: "This coupon has reached its usage limit" };
        }

        // Calculate discount
        const discountAmount = (originalPrice * coupon.discountPercentage) / 100;
        const finalPrice = Math.max(originalPrice - discountAmount, 0);

        const result: CouponValidationResult = {
          isValid: true,
          coupon: {
            id: coupon.id,
            code: coupon.code,
            description: coupon.description,
            discountPercentage: coupon.discountPercentage,
          },
          discountAmount,
          finalPrice,
        };

        // Cache the result
        this.couponCache.set(cacheKey, { ...result, originalPrice });
        this.cacheExpiry = now + this.CACHE_DURATION;

        return result;
      }

      // If no regular coupon found, check referral coupons
      console.log("🔍 [COUPON SERVICE] Checking referral coupons for code:", code);
      const referralCoupon = await prisma.referralCoupon.findUnique({
        where: { code },
        include: {
          couponUsages: {
            where: { userId },
          },
        },
      });

      console.log("🔍 [COUPON SERVICE] Referral coupon query result:", referralCoupon ? {
        id: referralCoupon.id,
        code: referralCoupon.code,
        isActive: referralCoupon.isActive,
        discountPercentage: referralCoupon.discountPercentage,
        currentUses: referralCoupon.currentUses,
        maxUses: referralCoupon.maxUses,
        expiresAt: referralCoupon.expiresAt
      } : "null");

      if (!referralCoupon) {
        console.log("❌ [COUPON SERVICE] No coupon found in either table");
        return { isValid: false, error: "Invalid coupon code" };
      }

      // Validate referral coupon
      if (!referralCoupon.isActive) {
        return { isValid: false, error: "This referral coupon is no longer active" };
      }

      // Check expiration
      if (referralCoupon.expiresAt && new Date() > referralCoupon.expiresAt) {
        return { isValid: false, error: "This referral coupon has expired" };
      }

      // Check if user has already used this referral coupon
      if (referralCoupon.couponUsages.length > 0) {
        return { isValid: false, error: "You have already used this referral coupon" };
      }

      // Check max uses
      if (referralCoupon.currentUses >= referralCoupon.maxUses) {
        return { isValid: false, error: "This referral coupon has reached its usage limit" };
      }

      // Calculate discount
      const discountAmount = (originalPrice * referralCoupon.discountPercentage) / 100;
      const finalPrice = Math.max(originalPrice - discountAmount, 0);

      const result: CouponValidationResult = {
        isValid: true,
        coupon: {
          id: referralCoupon.id,
          code: referralCoupon.code,
          description: `Referral Reward - ${referralCoupon.discountPercentage}% Off`,
          discountPercentage: referralCoupon.discountPercentage,
        },
        discountAmount,
        finalPrice,
        isReferralCoupon: true, // Flag to indicate this is a referral coupon
      };

      // Cache the result
      this.couponCache.set(cacheKey, { ...result, originalPrice });
      this.cacheExpiry = now + this.CACHE_DURATION;

      return result;
    } catch (error) {
      console.error("Error validating coupon:", error);
      return { isValid: false, error: "Failed to validate coupon" };
    }
  }

  /**
   * Apply a coupon and create usage record
   */
  async applyCoupon(
    code: string,
    userId: string,
    originalPrice: number,
    assignmentId?: string
  ): Promise<CouponApplicationResult> {
    try {
      // First validate the coupon
      const validation = await this.validateCoupon(code, userId, originalPrice);

      if (!validation.isValid || !validation.coupon) {
        return { success: false, error: validation.error };
      }

      // Check if this is a referral coupon
      const isReferralCoupon = (validation as any).isReferralCoupon;

      // Create coupon usage record and update coupon usage count in a transaction
      const result = await prisma.$transaction(async (tx) => {
        if (isReferralCoupon) {
          // Handle referral coupon
          const couponUsage = await tx.referralCouponUsage.create({
            data: {
              referralCouponId: validation.coupon!.id,
              userId,
              assignmentId,
              discountAmount: validation.discountAmount!,
              originalPrice,
              finalPrice: validation.finalPrice!,
            },
          });

          // Update referral coupon current uses
          await tx.referralCoupon.update({
            where: { id: validation.coupon!.id },
            data: {
              currentUses: {
                increment: 1,
              },
            },
          });

          return couponUsage;
        } else {
          // Handle regular coupon
          const couponUsage = await tx.couponUsage.create({
            data: {
              couponId: validation.coupon!.id,
              userId,
              assignmentId,
              discountAmount: validation.discountAmount!,
              originalPrice,
              finalPrice: validation.finalPrice!,
            },
          });

          // Update regular coupon current uses
          await tx.coupon.update({
            where: { id: validation.coupon!.id },
            data: {
              currentUses: {
                increment: 1,
              },
            },
          });

          return couponUsage;
        }
      });

      // Clear cache
      this.clearCache();

      return {
        success: true,
        couponUsageId: result.id,
        discountAmount: validation.discountAmount,
        finalPrice: validation.finalPrice,
      };
    } catch (error) {
      console.error("Error applying coupon:", error);
      return { success: false, error: "Failed to apply coupon" };
    }
  }

  /**
   * Check if user has used a specific coupon
   */
  async hasUserUsedCoupon(userId: string, couponCode: string): Promise<boolean> {
    try {
      const usage = await prisma.couponUsage.findFirst({
        where: {
          userId,
          coupon: {
            code: couponCode,
          },
        },
      });

      return !!usage;
    } catch (error) {
      console.error("Error checking coupon usage:", error);
      return false;
    }
  }

  /**
   * Get user's coupon usage history
   */
  async getUserCouponHistory(userId: string): Promise<Array<{
    id: string;
    couponId: string;
    userId: string;
    assignmentId: string | null;
    discountAmount: number;
    originalPrice: number;
    finalPrice: number;
    usedAt: Date;
    coupon: {
      code: string;
      description: string;
      discountPercentage: number;
    };
    assignment: {
      id: string;
      title: string;
      taskId: string;
    } | null;
  }>> {
    try {
      const usages = await prisma.couponUsage.findMany({
        where: { userId },
        include: {
          coupon: {
            select: {
              code: true,
              description: true,
              discountPercentage: true,
            },
          },
          assignment: {
            select: {
              id: true,
              title: true,
              taskId: true,
            },
          },
        },
        orderBy: {
          usedAt: 'desc',
        },
      });

      return usages;
    } catch (error) {
      console.error("Error fetching user coupon history:", error);
      return [];
    }
  }

  /**
   * Clear the cache
   */
  private clearCache(): void {
    this.couponCache.clear();
    this.cacheExpiry = 0;
  }
}

// Export singleton instance
export const couponService = new CouponService();

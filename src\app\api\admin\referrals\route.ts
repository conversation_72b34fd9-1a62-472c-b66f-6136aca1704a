import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError, checkPermission } from "@/lib/api-utils";
import { referralService } from "@/lib/referral-service";
import prisma from "@/lib/prisma";

// Get all referrals with statistics (Admin only)
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Only admins can access all referrals
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // Get referrals with pagination
    const [referrals, totalCount, stats] = await Promise.all([
      prisma.referral.findMany({
        skip,
        take: limit,
        include: {
          referrer: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          referralUsages: {
            include: {
              referredUser: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.referral.count(),
      referralService.getReferralStats()
    ]);

    const formattedReferrals = referrals.map(referral => ({
      id: referral.id,
      referrerId: referral.referrerId,
      referralCode: referral.referralCode,
      referralUrl: referral.referralUrl,
      isActive: referral.isActive,
      createdAt: referral.createdAt,
      updatedAt: referral.updatedAt,
      referrer: referral.referrer,
      referralUsages: referral.referralUsages.map(usage => ({
        id: usage.id,
        referralId: usage.referralId,
        referredUserId: usage.referredUserId,
        isConverted: usage.isConverted,
        usedAt: usage.usedAt,
        convertedAt: usage.convertedAt,
        referredUser: usage.referredUser,
      }))
    }));

    return apiSuccess({
      referrals: formattedReferrals,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
      stats,
    });
  } catch (error) {
    console.error("Error fetching admin referrals:", error);
    return apiError("Failed to fetch referrals", 500);
  }
}

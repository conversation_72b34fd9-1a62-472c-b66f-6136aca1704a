"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn, signOut } from "next-auth/react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { referralService } from "@/lib/referral-service";

interface ReferralAwareRegistrationFormProps extends React.HTMLAttributes<HTMLDivElement> {}

export function ReferralAwareRegistrationForm({ className, ...props }: ReferralAwareRegistrationFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<"CLIENT" | "WRITER">("CLIENT");
  const [referralCode, setReferralCode] = useState<string | null>(null);
  const [referralValid, setReferralValid] = useState<boolean | null>(null);
  const [referralData, setReferralData] = useState<any>(null);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Check for referral code in URL parameters
  useEffect(() => {
    const refParam = searchParams.get("ref");
    if (refParam) {
      setReferralCode(refParam);
      validateReferralCode(refParam);
    }
  }, [searchParams]);

  const validateReferralCode = async (code: string) => {
    try {
      const response = await fetch("/api/referrals/validate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ referralCode: code }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setReferralValid(true);
        setReferralData(data.data.referral);
        toast.success(`Welcome! You were referred by ${data.data.referral.referrer?.name || "a friend"}`);
      } else {
        setReferralValid(false);
        toast.error("Invalid referral code");
      }
    } catch (error) {
      console.error("Error validating referral:", error);
      setReferralValid(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    const formData = new FormData(e.currentTarget as HTMLFormElement);
    const firstName = formData.get("firstName") as string;
    const lastName = formData.get("lastName") as string;
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;
    const confirmPassword = formData.get("confirmPassword") as string;

    // Validation
    if (password.length < 8) {
      toast.error("Password must be at least 8 characters long");
      setIsLoading(false);
      return;
    }

    if (password !== confirmPassword) {
      toast.error("Passwords do not match");
      setIsLoading(false);
      return;
    }

    try {
      // Create user through NextAuth
      const res = await signIn("credentials", {
        email,
        password,
        role: selectedRole,
        name: `${firstName} ${lastName}`,
        callbackUrl: selectedRole === "CLIENT" ? "/client/dashboard" : "/writer/dashboard",
        redirect: false,
      });

      if (res?.error) {
        setError(res.error);
        toast.error(res.error || "Registration failed. Please try again.");
      } else {
        // Registration successful
        toast.success("Registration successful! Please check your email to verify your account.");
        
        // If there's a valid referral code, track the referral usage
        if (referralCode && referralValid) {
          try {
            // We need to get the user ID after registration
            // For now, we'll track this in the background
            // The actual conversion will be tracked when email is verified
            await fetch("/api/referrals/track-usage", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ 
                referralCode,
                userEmail: email // We'll use email to find the user later
              }),
            });
          } catch (referralError) {
            console.error("Error tracking referral:", referralError);
            // Don't show error to user as registration was successful
          }
        }

        // Redirect to appropriate login page
        router.push(selectedRole === "CLIENT" ? "/login/client" : "/login/writer");
      }
    } catch (err) {
      setError(`An unexpected error occurred: ${err}`);
      toast.error("Registration failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle social login with proper role storage
  const handleSocialLogin = async (provider: string) => {
    try {
      setIsLoading(true);

      // Clear any existing session to prevent confusion
      await signOut({ redirect: false });

      // Small delay to ensure session is cleared
      await new Promise(resolve => setTimeout(resolve, 100));

      // Include referral code in callback URL if present
      const callbackUrl = selectedRole === "CLIENT" 
        ? `/client/dashboard?intended_role=CLIENT&provider=${provider}${referralCode ? `&ref=${referralCode}` : ''}`
        : `/writer/dashboard?intended_role=WRITER&provider=${provider}${referralCode ? `&ref=${referralCode}` : ''}`;

      console.log(`🔧 Starting social login for ${selectedRole} role with provider:`, provider);

      await signIn(provider, {
        callbackUrl,
        redirect: true,
      });
    } catch (error) {
      console.error("Social login error:", error);
      toast.error(`${provider} registration failed. Please try again.`);
      setIsLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      {/* Referral Banner */}
      {referralCode && (
        <Card className={cn(
          "border-2",
          referralValid === true ? "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950" :
          referralValid === false ? "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950" :
          "border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950"
        )}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Badge variant={referralValid === true ? "default" : referralValid === false ? "destructive" : "secondary"}>
                  Referral Code: {referralCode}
                </Badge>
                {referralValid === true && referralData && (
                  <span className="text-sm text-muted-foreground">
                    Referred by {referralData.referrer?.name || referralData.referrer?.email}
                  </span>
                )}
              </div>
              {referralValid === true && (
                <Badge variant="outline" className="text-green-600 border-green-600">
                  Valid ✓
                </Badge>
              )}
            </div>
            {referralValid === true && (
              <p className="text-sm text-muted-foreground mt-2">
                You'll earn rewards when you complete your registration!
              </p>
            )}
          </CardContent>
        </Card>
      )}

      <Card className="overflow-hidden">
        <CardContent className="grid p-0 xs:grid-cols-1 md:grid-cols-2">
          <form className="p-4 xs:p-6 md:p-8" onSubmit={handleSubmit}>
            <div className="flex flex-col gap-5">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">Create Account</h1>
                <p className="text-balance text-muted-foreground">
                  Join our platform as a client or writer
                </p>
              </div>

              {/* Role Selection */}
              <div className="grid gap-2">
                <Label htmlFor="role">I want to register as:</Label>
                <Select value={selectedRole} onValueChange={(value: "CLIENT" | "WRITER") => setSelectedRole(value)}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Select your role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CLIENT">Client - Post assignments and find writers</SelectItem>
                    <SelectItem value="WRITER">Writer - Find assignments and earn money</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-4 xs:grid-cols-1 sm:grid-cols-2">
                <div className="grid gap-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    placeholder="John"
                    required
                    className="h-10"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    placeholder="Doe"
                    required
                    className="h-10"
                  />
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  className="h-10"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="phone">Phone Number (Optional)</Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  placeholder="+****************"
                  className="h-10"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Enter your password"
                  required
                  className="h-10"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  placeholder="Confirm your password"
                  required
                  className="h-10"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox id="terms" required />
                <label
                  htmlFor="terms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  I agree to the{" "}
                  <Link href="/terms" className="underline underline-offset-4 hover:text-primary">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link href="/privacy" className="underline underline-offset-4 hover:text-primary">
                    Privacy Policy
                  </Link>
                </label>
              </div>

              {error && (
                <div className="text-sm text-red-600 bg-red-50 dark:bg-red-950 p-3 rounded-md">
                  {error}
                </div>
              )}

              <Button type="submit" className="w-full h-10" disabled={isLoading}>
                {isLoading ? "Creating Account..." : "Create Account"}
              </Button>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">Or continue with</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  type="button"
                  disabled={isLoading}
                  onClick={() => handleSocialLogin("google")}
                  className="h-10"
                >
                  <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                    <path
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      fill="#4285F4"
                    />
                    <path
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      fill="#34A853"
                    />
                    <path
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      fill="#FBBC05"
                    />
                    <path
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      fill="#EA4335"
                    />
                  </svg>
                  Google
                </Button>
                <Button
                  variant="outline"
                  type="button"
                  disabled={isLoading}
                  onClick={() => handleSocialLogin("facebook")}
                  className="h-10"
                >
                  <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                  </svg>
                  Facebook
                </Button>
              </div>

              <div className="text-center text-sm">
                Already have an account?{" "}
                <Link 
                  href={selectedRole === "CLIENT" ? "/login/client" : "/login/writer"} 
                  className="underline underline-offset-4 hover:text-primary"
                >
                  Sign in
                </Link>
              </div>
            </div>
          </form>

          <div className="relative hidden bg-muted md:block">
            <img
              src="/placeholder.svg"
              alt="Registration"
              className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Users,
  Gift,
  CheckCircle,
  Clock,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Copy
} from "lucide-react";
import { toast } from "sonner";
import { ReferralData, ReferralStatsData } from "@/types/api";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";


export function AdminReferralsContent() {
  const [referrals, setReferrals] = useState<ReferralData[]>([]);
  const [stats, setStats] = useState<ReferralStatsData>({
    totalReferrals: 0,
    activeReferrals: 0,
    totalConversions: 0,
    conversionRate: 0,
    totalCouponsGenerated: 0,
    totalCouponsUsed: 0,
  });
  const [loading, setLoading] = useState(true);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/referrals");

      if (response.ok) {
        const data = await response.json();
        setReferrals(data.data.referrals || []);
        setStats(data.data.stats || stats);
      } else {
        toast.error("Failed to load referral data");
      }
    } catch (error) {
      console.error("Error fetching referral data:", error);
      toast.error("Failed to load referral data");
    } finally {
      setLoading(false);
    }
  }, [stats]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const toggleRowExpansion = (referralId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(referralId)) {
      newExpanded.delete(referralId);
    } else {
      newExpanded.add(referralId);
    }
    setExpandedRows(newExpanded);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy:", error);
      toast.error("Failed to copy to clipboard");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading referrals...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4 lg:p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Referral Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage the referral program
          </p>
        </div>
        <Button onClick={fetchData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Referrals</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalReferrals}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeReferrals} active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Successful Conversions</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalConversions}</div>
            <p className="text-xs text-muted-foreground">
              {stats.conversionRate}% conversion rate
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reward Coupons</CardTitle>
            <Gift className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCouponsGenerated}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalCouponsUsed} used
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Referrals Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Referrals</CardTitle>
          <CardDescription>
            Complete list of referral links and their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {referrals.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No referrals found</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]"></TableHead>
                  <TableHead>Referrer</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Conversions</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {referrals.map((referral) => (
                  <React.Fragment key={referral.id}>
                    <TableRow>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleRowExpansion(referral.id)}
                          className="p-0 h-8 w-8"
                        >
                          {expandedRows.has(referral.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {referral.referrer?.name || "Unknown"}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {referral.referrer?.email}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="bg-muted px-2 py-1 rounded text-sm">
                          {referral.referralCode}
                        </code>
                      </TableCell>
                      <TableCell>
                        <Badge variant={referral.isActive ? "default" : "secondary"}>
                          {referral.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">
                            {referral.referralUsages?.filter(usage => usage.isConverted).length || 0}
                          </span>
                          <span className="text-muted-foreground">
                            / {referral.referralUsages?.length || 0}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {new Date(referral.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(referral.referralUrl)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    {expandedRows.has(referral.id) && (
                      <TableRow>
                        <TableCell colSpan={7} className="p-0">
                          <div className="p-4 bg-muted/50">
                            <div className="space-y-4">
                              <div>
                                <h4 className="font-medium mb-2">Referral URL:</h4>
                                <div className="flex items-center space-x-2">
                                  <code className="bg-background px-3 py-2 rounded border text-sm flex-1">
                                    {referral.referralUrl}
                                  </code>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => copyToClipboard(referral.referralUrl)}
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                              
                              {referral.referralUsages && referral.referralUsages.length > 0 && (
                                <div>
                                  <h4 className="font-medium mb-2">Referral Activity:</h4>
                                  <div className="space-y-2">
                                    {referral.referralUsages.map((usage) => (
                                      <div key={usage.id} className="flex items-center justify-between p-2 bg-background rounded border">
                                        <div className="flex items-center space-x-3">
                                          {usage.isConverted ? (
                                            <CheckCircle className="h-4 w-4 text-green-500" />
                                          ) : (
                                            <Clock className="h-4 w-4 text-yellow-500" />
                                          )}
                                          <div>
                                            <div className="font-medium">
                                              {usage.referredUser?.name || "Unknown User"}
                                            </div>
                                            <div className="text-sm text-muted-foreground">
                                              {usage.referredUser?.email}
                                            </div>
                                          </div>
                                        </div>
                                        <div className="text-right text-sm">
                                          <div>
                                            {usage.isConverted ? "Converted" : "Pending"}
                                          </div>
                                          <div className="text-muted-foreground">
                                            {new Date(usage.usedAt).toLocaleDateString()}
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

"use client";
import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableHead, TableRow, TableHeader, TableBody, TableCell } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "sonner";
import { PricingSettings } from "@/components/admin/pricing-settings";
import { CouponSettings } from "@/components/admin/coupon-settings";
import { ReferralSettings } from "@/components/admin/referral-settings";
import { AuthorsSettings } from "@/components/admin/authors-settings";
import { CategoriesSettings } from "@/components/admin/categories-settings";
import { CompanyInfoSettings } from "@/components/admin/company-info-settings";
import { FAQSettings } from "@/components/admin/faq-settings";

interface MultipleChoiceQuestion {
  question: string;
  options: string[];
  correctAnswer: string;
}

interface EssayExam {
  topic: string;
  rubrics: string;
}

interface Assessment {
  id: string;
  title: string;
  multipleChoiceQuiz: MultipleChoiceQuestion[];
  essayExam: EssayExam;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

type AssessmentFormState = {
  title: string;
  multipleChoiceQuiz: MultipleChoiceQuestion[];
  essayExam: EssayExam;
};

const defaultAssessmentForm: AssessmentFormState = {
  title: "",
  multipleChoiceQuiz: [
    { question: "", options: ["", "", "", ""], correctAnswer: "" }
  ],
  essayExam: { topic: "", rubrics: "" }
};

export default function AdminSettingsPage() {
  const [tab, setTab] = React.useState("assessments");
  const [assessments, setAssessments] = React.useState<Assessment[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [editId, setEditId] = React.useState<string|null>(null);
  const [form, setForm] = React.useState<AssessmentFormState>(defaultAssessmentForm);

  React.useEffect(() => {
    fetchAssessments();
  }, []);

  async function fetchAssessments() {
    setLoading(true);
    const res = await fetch("/api/admin/assessments");
    if (res.ok) {
      setAssessments(await res.json());
    } else {
      toast.error("Failed to fetch assessments");
    }
    setLoading(false);
  }

  function resetForm() {
    setForm(defaultAssessmentForm);
    setEditId(null);
  }

  function handleMCQChange(idx: number, field: keyof MultipleChoiceQuestion, value: string) {
    setForm((prev) => {
      const quiz = [...prev.multipleChoiceQuiz];
      if (field === "options") return prev; // options handled separately
      quiz[idx] = { ...quiz[idx], [field]: value };
      return { ...prev, multipleChoiceQuiz: quiz };
    });
  }

  function handleMCQOptionChange(idx: number, optIdx: number, value: string) {
    setForm((prev) => {
      const quiz = [...prev.multipleChoiceQuiz];
      const options = [...quiz[idx].options];
      options[optIdx] = value;
      quiz[idx] = { ...quiz[idx], options };
      return { ...prev, multipleChoiceQuiz: quiz };
    });
  }

  function addMCQ() {
    setForm((prev) => ({
      ...prev,
      multipleChoiceQuiz: [
        ...prev.multipleChoiceQuiz,
        { question: "", options: ["", "", "", ""], correctAnswer: "" }
      ]
    }));
  }

  function removeMCQ(idx: number) {
    setForm((prev) => ({
      ...prev,
      multipleChoiceQuiz: prev.multipleChoiceQuiz.filter((_, i) => i !== idx)
    }));
  }

  function handleEssayChange(field: keyof EssayExam, value: string) {
    setForm((prev) => ({
      ...prev,
      essayExam: { ...prev.essayExam, [field]: value }
    }));
  }

  function openEditDialog(assessment: Assessment) {
    setEditId(assessment.id);
    setForm({
      title: assessment.title,
      multipleChoiceQuiz: assessment.multipleChoiceQuiz,
      essayExam: assessment.essayExam
    });
    setDialogOpen(true);
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    const method = editId ? "PUT" : "POST";
    const url = editId ? `/api/admin/assessments/${editId}` : "/api/admin/assessments";
    const res = await fetch(url, {
      method,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(form)
    });
    if (res.ok) {
      toast.success(editId ? "Assessment updated" : "Assessment created");
      setDialogOpen(false);
      resetForm();
      fetchAssessments();
    } else {
      toast.error("Failed to save assessment");
    }
    setLoading(false);
  }

  async function handleDelete(id: string) {
    if (!window.confirm("Delete this assessment?")) return;
    setLoading(true);
    const res = await fetch(`/api/admin/assessments/${id}`, { method: "DELETE" });
    if (res.ok) {
      toast.success("Assessment deleted");
      fetchAssessments();
    } else {
      toast.error("Failed to delete assessment");
    }
    setLoading(false);
  }

  async function handleToggleActive(id: string, isActive: boolean) {
    setLoading(true);
    const res = await fetch(`/api/admin/assessments/${id}/toggle-active`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ isActive })
    });
    if (res.ok) {
      toast.success(isActive ? "Assessment activated" : "Assessment deactivated");
      fetchAssessments();
    } else {
      toast.error("Failed to toggle assessment status");
    }
    setLoading(false);
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8">
      <Tabs value={tab} onValueChange={setTab} className="w-full">
        {/* Top row - 4 tabs */}
        <div className="space-y-4 mb-6">
          <TabsList className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-1 sm:gap-2 bg-muted/50 h-auto p-1">
            <TabsTrigger
              value="assessments"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 text-xs sm:text-sm px-2 py-2"
            >
              Assessments
            </TabsTrigger>
            <TabsTrigger
              value="pricing"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 text-xs sm:text-sm px-2 py-2"
            >
              <span className="hidden sm:inline">Pricing Strategy</span>
              <span className="sm:hidden">Pricing</span>
            </TabsTrigger>
            <TabsTrigger
              value="coupons"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 text-xs sm:text-sm px-2 py-2"
            >
              Coupons
            </TabsTrigger>
            <TabsTrigger
              value="referrals"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 text-xs sm:text-sm px-2 py-2"
            >
              Referrals
            </TabsTrigger>
          </TabsList>

          {/* Bottom row - 4 tabs */}
          <TabsList className="w-full grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-1 sm:gap-2 bg-muted/50 h-auto p-1">
            <TabsTrigger
              value="authors"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 text-xs sm:text-sm px-2 py-2"
            >
              Authors
            </TabsTrigger>
            <TabsTrigger
              value="categories"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 text-xs sm:text-sm px-2 py-2"
            >
              Categories
            </TabsTrigger>
            <TabsTrigger
              value="faqs"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 text-xs sm:text-sm px-2 py-2"
            >
              FAQs
            </TabsTrigger>
            <TabsTrigger
              value="company"
              className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300 text-xs sm:text-sm px-2 py-2"
            >
              <span className="hidden sm:inline">Company Info</span>
              <span className="sm:hidden">Company</span>
            </TabsTrigger>
          </TabsList>
        </div>
        <TabsContent value="assessments">
          <Card className="w-full dark:bg-muted bg-white">
            <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <CardTitle>Assessments</CardTitle>
              <Dialog open={dialogOpen} onOpenChange={(open) => { setDialogOpen(open); if (!open) resetForm(); }}>
                <DialogTrigger asChild>
                  <Button onClick={() => { setEditId(null); setDialogOpen(true); }} className="w-full sm:w-auto">
                    New Assessment
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl w-[95vw] sm:w-full max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>{editId ? "Edit Assessment" : "New Assessment"}</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <Label htmlFor="title">Title</Label>
                      <Input id="title" value={form.title} onChange={e => setForm(f => ({ ...f, title: e.target.value }))} required />
                    </div>
                    <div>
                      <Label>Multiple Choice Questions</Label>
                      {form.multipleChoiceQuiz.map((mcq, idx) => (
                        <div key={idx} className="border rounded p-3 mb-3 bg-muted/30">
                          <Label>Question {idx + 1}</Label>
                          <Input className="mb-2" value={mcq.question} onChange={e => handleMCQChange(idx, "question", e.target.value)} required />
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-2">
                            {mcq.options.map((opt, optIdx) => (
                              <Input key={optIdx} placeholder={`Option ${optIdx + 1}`} value={opt} onChange={e => handleMCQOptionChange(idx, optIdx, e.target.value)} required />
                            ))}
                          </div>
                          <Input className="mb-2" placeholder="Correct Answer (copy one option)" value={mcq.correctAnswer} onChange={e => handleMCQChange(idx, "correctAnswer", e.target.value)} required />
                          <div className="flex flex-col sm:flex-row gap-2">
                            <Button type="button" size="sm" variant="outline" onClick={() => addMCQ()} className="w-full sm:w-auto">Add</Button>
                            {form.multipleChoiceQuiz.length > 1 && (
                              <Button type="button" size="sm" variant="destructive" onClick={() => removeMCQ(idx)} className="w-full sm:w-auto">Remove</Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    <div>
                      <Label>Essay Exam</Label>
                      <Input className="mb-2" placeholder="Essay Topic" value={form.essayExam.topic} onChange={e => handleEssayChange("topic", e.target.value)} required />
                      <Textarea placeholder="Rubrics/Instructions" value={form.essayExam.rubrics} onChange={e => handleEssayChange("rubrics", e.target.value)} required />
                    </div>
                    <DialogFooter className="flex-col sm:flex-row gap-2">
                      <Button type="submit" className="w-full sm:w-auto">{loading ? (editId ? "Updating..." : "Creating...") : (editId ? "Update" : "Create")}</Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">Loading...</div>
              ) : (
                <div className="overflow-x-auto">
                  <Table className="w-full min-w-[600px]">
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>MCQs</TableHead>
                        <TableHead className="hidden sm:table-cell">Essay Topic</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {assessments.map(a => (
                        <TableRow key={a.id}>
                          <TableCell className="font-medium">{a.title}</TableCell>
                          <TableCell>{a.multipleChoiceQuiz.length}</TableCell>
                          <TableCell className="hidden sm:table-cell max-w-[200px] truncate" title={a.essayExam.topic}>
                            {a.essayExam.topic.length > 40 ? a.essayExam.topic.slice(0, 40) + '…' : a.essayExam.topic}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div>
                                      <Switch
                                        checked={a.isActive}
                                        onCheckedChange={(checked) => handleToggleActive(a.id, checked)}
                                        disabled={loading}
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="text-sm">
                                      {a.isActive
                                        ? "This assessment is currently active and visible to writers. Click to deactivate."
                                        : "Click to make this assessment active. Only one assessment can be active at a time."
                                      }
                                    </p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <Badge variant={a.isActive ? "default" : "secondary"}>
                                {a.isActive ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col sm:flex-row gap-1 sm:gap-2">
                              <Button size="sm" variant="outline" onClick={() => openEditDialog(a)} className="w-full sm:w-auto">Edit</Button>
                              <Button size="sm" variant="destructive" onClick={() => handleDelete(a.id)} className="w-full sm:w-auto">Delete</Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="pricing">
          <PricingSettings />
        </TabsContent>
        <TabsContent value="coupons">
          <CouponSettings />
        </TabsContent>
        <TabsContent value="referrals">
          <ReferralSettings />
        </TabsContent>
        <TabsContent value="authors">
          <AuthorsSettings />
        </TabsContent>
        <TabsContent value="categories">
          <CategoriesSettings />
        </TabsContent>
        <TabsContent value="faqs">
          <FAQSettings />
        </TabsContent>
        <TabsContent value="company">
          <CompanyInfoSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
}

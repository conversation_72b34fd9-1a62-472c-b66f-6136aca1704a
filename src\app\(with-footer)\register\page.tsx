import { Metadata } from "next";
import { getRouteMetadata } from "@/lib/route-metadata";
import { ReferralAwareRegistrationForm } from "@/components/referral-aware-registration-form";

export async function generateMetadata(): Promise<Metadata> {
  return await getRouteMetadata("/register");
}

export default function RegisterPage() {
  return (
    <div className="container flex h-fit w-full items-center justify-center px-4 py-8">
      <ReferralAwareRegistrationForm className="w-full max-w-4xl" />
    </div>
  );
}

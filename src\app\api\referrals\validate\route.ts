import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError } from "@/lib/api-utils";
import { referralService } from "@/lib/referral-service";
import { referralValidationSchema } from "@/lib/validations";

// Validate referral code (Public endpoint)
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    const parsed = referralValidationSchema.safeParse(body);
    
    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { referralCode } = parsed.data;

    const result = await referralService.validateReferral(referralCode);

    if (!result.isValid) {
      return apiError(result.error || "Invalid referral code", 400);
    }

    return apiSuccess({
      referral: result.referral,
      isValid: true,
    });
  } catch (error) {
    console.error("Error validating referral:", error);
    return apiError("Failed to validate referral", 500);
  }
}

// src/lib/referral-service.ts
import prisma from "./prisma";
import { ReferralData, ReferralUsageData, ReferralCouponData, ReferralStatsData } from "@/types/api";

export interface ReferralValidationResult {
  isValid: boolean;
  referral?: ReferralData;
  error?: string;
}

export interface ReferralCreateResult {
  success: boolean;
  referral?: ReferralData;
  error?: string;
}

export interface ReferralCouponValidationResult {
  isValid: boolean;
  coupon?: {
    id: string;
    code: string;
    discountPercentage: number;
  };
  discountAmount?: number;
  finalPrice?: number;
  error?: string;
}

export interface ReferralCouponApplicationResult {
  success: boolean;
  couponUsageId?: string;
  discountAmount?: number;
  finalPrice?: number;
  error?: string;
}

class ReferralService {
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private referralCache = new Map<string, ReferralValidationResult & { timestamp: number }>();
  private couponCache = new Map<string, ReferralCouponValidationResult & { timestamp: number; originalPrice: number }>();

  /**
   * Generate a unique referral code based on user input
   */
  private generateReferralCode(baseCode: string): string {
    // Clean and format the base code
    const cleanCode = baseCode
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
    
    return cleanCode;
  }

  /**
   * Generate a unique referral coupon code
   */
  private generateCouponCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 3; i++) {
      if (i > 0) result += '-';
      for (let j = 0; j < 3; j++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
    }
    return result;
  }

  /**
   * Create a new referral link
   */
  async createReferral(userId: string, referralCode: string): Promise<ReferralCreateResult> {
    try {
      const cleanCode = this.generateReferralCode(referralCode);
      
      // Check if referral code already exists
      const existingReferral = await prisma.referral.findUnique({
        where: { referralCode: cleanCode }
      });

      if (existingReferral) {
        return { success: false, error: "This referral code is already taken. Please choose a different one." };
      }

      // Get base URL from environment
      const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";
      const referralUrl = `${baseUrl}/register?ref=${cleanCode}`;

      // Create the referral
      const referral = await prisma.referral.create({
        data: {
          referrerId: userId,
          referralCode: cleanCode,
          referralUrl,
        },
        include: {
          referrer: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      });

      return { 
        success: true, 
        referral: {
          id: referral.id,
          referrerId: referral.referrerId,
          referralCode: referral.referralCode,
          referralUrl: referral.referralUrl,
          isActive: referral.isActive,
          createdAt: referral.createdAt,
          updatedAt: referral.updatedAt,
          referrer: referral.referrer,
        }
      };
    } catch (error) {
      console.error("Error creating referral:", error);
      return { success: false, error: "Failed to create referral link" };
    }
  }

  /**
   * Validate a referral code
   */
  async validateReferral(referralCode: string): Promise<ReferralValidationResult> {
    try {
      // Check cache first
      const cacheKey = referralCode.toLowerCase();
      const now = Date.now();
      const cached = this.referralCache.get(cacheKey);

      if (cached && (now - cached.timestamp) < this.CACHE_DURATION) {
        return { isValid: cached.isValid, referral: cached.referral, error: cached.error };
      }

      // Find the referral
      const referral = await prisma.referral.findUnique({
        where: { referralCode: referralCode.toLowerCase() },
        include: {
          referrer: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      });

      if (!referral) {
        const result = { isValid: false, error: "Invalid referral code" };
        this.referralCache.set(cacheKey, { ...result, timestamp: now });
        return result;
      }

      if (!referral.isActive) {
        const result = { isValid: false, error: "This referral link is no longer active" };
        this.referralCache.set(cacheKey, { ...result, timestamp: now });
        return result;
      }

      const result: ReferralValidationResult = {
        isValid: true,
        referral: {
          id: referral.id,
          referrerId: referral.referrerId,
          referralCode: referral.referralCode,
          referralUrl: referral.referralUrl,
          isActive: referral.isActive,
          createdAt: referral.createdAt,
          updatedAt: referral.updatedAt,
          referrer: referral.referrer,
        }
      };

      // Cache the result
      this.referralCache.set(cacheKey, { ...result, timestamp: now });
      return result;
    } catch (error) {
      console.error("Error validating referral:", error);
      return { isValid: false, error: "Failed to validate referral code" };
    }
  }

  /**
   * Track referral usage when someone clicks a referral link
   */
  async trackReferralUsage(referralCode: string, referredUserId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const referral = await prisma.referral.findUnique({
        where: { referralCode: referralCode.toLowerCase() }
      });

      if (!referral) {
        return { success: false, error: "Invalid referral code" };
      }

      // Check if this user has already been tracked for this referral
      const existingUsage = await prisma.referralUsage.findUnique({
        where: {
          referralId_referredUserId: {
            referralId: referral.id,
            referredUserId,
          }
        }
      });

      if (existingUsage) {
        return { success: true }; // Already tracked, no need to create duplicate
      }

      // Create referral usage record
      await prisma.referralUsage.create({
        data: {
          referralId: referral.id,
          referredUserId,
        }
      });

      return { success: true };
    } catch (error) {
      console.error("Error tracking referral usage:", error);
      return { success: false, error: "Failed to track referral usage" };
    }
  }

  /**
   * Mark referral as converted and generate reward coupon
   */
  async convertReferral(referralCode: string, referredUserId: string): Promise<{ success: boolean; coupon?: ReferralCouponData; error?: string }> {
    try {
      const referral = await prisma.referral.findUnique({
        where: { referralCode: referralCode.toLowerCase() }
      });

      if (!referral) {
        return { success: false, error: "Invalid referral code" };
      }

      // Get referral settings
      const settings = await this.getReferralSettings();

      const result = await prisma.$transaction(async (tx) => {
        // Update referral usage to mark as converted
        const referralUsage = await tx.referralUsage.updateMany({
          where: {
            referralId: referral.id,
            referredUserId,
            isConverted: false,
          },
          data: {
            isConverted: true,
            convertedAt: new Date(),
          }
        });

        if (referralUsage.count === 0) {
          throw new Error("Referral usage not found or already converted");
        }

        // Generate unique coupon code
        let couponCode: string;
        let isUnique = false;
        let attempts = 0;
        const maxAttempts = 10;

        do {
          couponCode = this.generateCouponCode();
          const existing = await tx.referralCoupon.findUnique({ where: { code: couponCode } });
          isUnique = !existing;
          attempts++;
        } while (!isUnique && attempts < maxAttempts);

        if (!isUnique) {
          throw new Error("Failed to generate unique coupon code");
        }

        // Create referral coupon
        const expiresAt = settings.couponExpirationDays 
          ? new Date(Date.now() + settings.couponExpirationDays * 24 * 60 * 60 * 1000)
          : null;

        const referralCoupon = await tx.referralCoupon.create({
          data: {
            referrerId: referral.referrerId,
            referredUserId,
            code: couponCode,
            discountPercentage: settings.discountPercentage,
            maxUses: settings.maxUsesPerCoupon,
            expiresAt,
          },
          include: {
            referrer: {
              select: {
                id: true,
                name: true,
                email: true,
              }
            },
            referredUser: {
              select: {
                id: true,
                name: true,
                email: true,
              }
            }
          }
        });

        return referralCoupon;
      });

      return { 
        success: true, 
        coupon: {
          id: result.id,
          referrerId: result.referrerId,
          referredUserId: result.referredUserId,
          code: result.code,
          discountPercentage: result.discountPercentage,
          maxUses: result.maxUses,
          currentUses: result.currentUses,
          isActive: result.isActive,
          expiresAt: result.expiresAt,
          createdAt: result.createdAt,
          updatedAt: result.updatedAt,
          referrer: result.referrer,
          referredUser: result.referredUser,
        }
      };
    } catch (error) {
      console.error("Error converting referral:", error);
      return { success: false, error: "Failed to convert referral" };
    }
  }

  /**
   * Get referral settings (with defaults if not found)
   */
  async getReferralSettings(): Promise<{ discountPercentage: number; maxUsesPerCoupon: number; couponExpirationDays: number | null; isActive: boolean }> {
    try {
      const settings = await prisma.referralSettings.findFirst();
      
      if (!settings) {
        // Return default settings
        return {
          discountPercentage: 10.0,
          maxUsesPerCoupon: 2,
          couponExpirationDays: null,
          isActive: true,
        };
      }

      return {
        discountPercentage: settings.discountPercentage,
        maxUsesPerCoupon: settings.maxUsesPerCoupon,
        couponExpirationDays: settings.couponExpirationDays,
        isActive: settings.isActive,
      };
    } catch (error) {
      console.error("Error getting referral settings:", error);
      // Return default settings on error
      return {
        discountPercentage: 10.0,
        maxUsesPerCoupon: 2,
        couponExpirationDays: null,
        isActive: true,
      };
    }
  }

  /**
   * Get user's referrals with usage statistics
   */
  async getUserReferrals(userId: string): Promise<ReferralData[]> {
    try {
      const referrals = await prisma.referral.findMany({
        where: { referrerId: userId },
        include: {
          referrer: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          referralUsages: {
            include: {
              referredUser: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return referrals.map(referral => ({
        id: referral.id,
        referrerId: referral.referrerId,
        referralCode: referral.referralCode,
        referralUrl: referral.referralUrl,
        isActive: referral.isActive,
        createdAt: referral.createdAt,
        updatedAt: referral.updatedAt,
        referrer: referral.referrer,
        referralUsages: referral.referralUsages.map(usage => ({
          id: usage.id,
          referralId: usage.referralId,
          referredUserId: usage.referredUserId,
          isConverted: usage.isConverted,
          usedAt: usage.usedAt,
          convertedAt: usage.convertedAt,
          referredUser: usage.referredUser,
        }))
      }));
    } catch (error) {
      console.error("Error getting user referrals:", error);
      return [];
    }
  }

  /**
   * Get user's referral coupons
   */
  async getUserReferralCoupons(userId: string): Promise<ReferralCouponData[]> {
    try {
      const coupons = await prisma.referralCoupon.findMany({
        where: { referrerId: userId },
        include: {
          referrer: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          referredUser: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return coupons.map(coupon => ({
        id: coupon.id,
        referrerId: coupon.referrerId,
        referredUserId: coupon.referredUserId,
        code: coupon.code,
        discountPercentage: coupon.discountPercentage,
        maxUses: coupon.maxUses,
        currentUses: coupon.currentUses,
        isActive: coupon.isActive,
        expiresAt: coupon.expiresAt,
        createdAt: coupon.createdAt,
        updatedAt: coupon.updatedAt,
        referrer: coupon.referrer,
        referredUser: coupon.referredUser,
      }));
    } catch (error) {
      console.error("Error getting user referral coupons:", error);
      return [];
    }
  }

  /**
   * Validate referral coupon
   */
  async validateReferralCoupon(code: string, userId: string, originalPrice: number): Promise<ReferralCouponValidationResult> {
    try {
      if (typeof originalPrice !== 'number' || originalPrice < 0) {
        return { isValid: false, error: "Invalid price amount" };
      }

      // Check cache first
      const cacheKey = `${code}-${userId}`;
      const now = Date.now();
      const cached = this.couponCache.get(cacheKey);

      if (cached && (now - cached.timestamp) < this.CACHE_DURATION && cached.originalPrice === originalPrice) {
        return {
          isValid: cached.isValid,
          coupon: cached.coupon,
          discountAmount: cached.discountAmount,
          finalPrice: cached.finalPrice,
          error: cached.error
        };
      }

      // Find the coupon
      const coupon = await prisma.referralCoupon.findUnique({
        where: { code },
        include: {
          couponUsages: {
            where: { userId },
          },
        },
      });

      if (!coupon) {
        const result = { isValid: false, error: "Invalid referral coupon code" };
        this.couponCache.set(cacheKey, { ...result, timestamp: now, originalPrice });
        return result;
      }

      if (!coupon.isActive) {
        const result = { isValid: false, error: "This referral coupon is no longer active" };
        this.couponCache.set(cacheKey, { ...result, timestamp: now, originalPrice });
        return result;
      }

      if (coupon.expiresAt && coupon.expiresAt < new Date()) {
        const result = { isValid: false, error: "This referral coupon has expired" };
        this.couponCache.set(cacheKey, { ...result, timestamp: now, originalPrice });
        return result;
      }

      if (coupon.currentUses >= coupon.maxUses) {
        const result = { isValid: false, error: "This referral coupon has reached its usage limit" };
        this.couponCache.set(cacheKey, { ...result, timestamp: now, originalPrice });
        return result;
      }

      // Check if user has already used this coupon
      if (coupon.couponUsages.length > 0) {
        const result = { isValid: false, error: "You have already used this referral coupon" };
        this.couponCache.set(cacheKey, { ...result, timestamp: now, originalPrice });
        return result;
      }

      // Calculate discount
      const discountAmount = (originalPrice * coupon.discountPercentage) / 100;
      const finalPrice = Math.max(originalPrice - discountAmount, 0);

      const result: ReferralCouponValidationResult = {
        isValid: true,
        coupon: {
          id: coupon.id,
          code: coupon.code,
          discountPercentage: coupon.discountPercentage,
        },
        discountAmount,
        finalPrice,
      };

      // Cache the result
      this.couponCache.set(cacheKey, { ...result, timestamp: now, originalPrice });
      return result;
    } catch (error) {
      console.error("Error validating referral coupon:", error);
      return { isValid: false, error: "Failed to validate referral coupon" };
    }
  }

  /**
   * Apply referral coupon and create usage record
   */
  async applyReferralCoupon(
    code: string,
    userId: string,
    originalPrice: number,
    assignmentId?: string
  ): Promise<ReferralCouponApplicationResult> {
    try {
      // First validate the coupon
      const validation = await this.validateReferralCoupon(code, userId, originalPrice);

      if (!validation.isValid || !validation.coupon) {
        return { success: false, error: validation.error };
      }

      // Create coupon usage record and update coupon usage count in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create usage record
        const couponUsage = await tx.referralCouponUsage.create({
          data: {
            referralCouponId: validation.coupon!.id,
            userId,
            assignmentId,
            discountAmount: validation.discountAmount!,
            originalPrice,
            finalPrice: validation.finalPrice!,
          },
        });

        // Update coupon current uses
        await tx.referralCoupon.update({
          where: { id: validation.coupon!.id },
          data: {
            currentUses: {
              increment: 1,
            },
          },
        });

        return couponUsage;
      });

      // Clear cache
      this.clearCache();

      return {
        success: true,
        couponUsageId: result.id,
        discountAmount: validation.discountAmount,
        finalPrice: validation.finalPrice,
      };
    } catch (error) {
      console.error("Error applying referral coupon:", error);
      return { success: false, error: "Failed to apply referral coupon" };
    }
  }

  /**
   * Get referral statistics for admin dashboard
   */
  async getReferralStats(): Promise<ReferralStatsData> {
    try {
      const [
        totalReferrals,
        activeReferrals,
        totalConversions,
        totalCouponsGenerated,
        totalCouponsUsed
      ] = await Promise.all([
        prisma.referral.count(),
        prisma.referral.count({ where: { isActive: true } }),
        prisma.referralUsage.count({ where: { isConverted: true } }),
        prisma.referralCoupon.count(),
        prisma.referralCouponUsage.count()
      ]);

      const conversionRate = totalReferrals > 0 ? (totalConversions / totalReferrals) * 100 : 0;

      return {
        totalReferrals,
        activeReferrals,
        totalConversions,
        conversionRate: Math.round(conversionRate * 100) / 100, // Round to 2 decimal places
        totalCouponsGenerated,
        totalCouponsUsed,
      };
    } catch (error) {
      console.error("Error getting referral stats:", error);
      return {
        totalReferrals: 0,
        activeReferrals: 0,
        totalConversions: 0,
        conversionRate: 0,
        totalCouponsGenerated: 0,
        totalCouponsUsed: 0,
      };
    }
  }

  /**
   * Update referral settings
   */
  async updateReferralSettings(settings: {
    discountPercentage?: number;
    maxUsesPerCoupon?: number;
    couponExpirationDays?: number | null;
    isActive?: boolean;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      // Check if settings exist
      const existingSettings = await prisma.referralSettings.findFirst();

      if (existingSettings) {
        await prisma.referralSettings.update({
          where: { id: existingSettings.id },
          data: settings,
        });
      } else {
        await prisma.referralSettings.create({
          data: {
            discountPercentage: settings.discountPercentage ?? 10.0,
            maxUsesPerCoupon: settings.maxUsesPerCoupon ?? 2,
            couponExpirationDays: settings.couponExpirationDays ?? null,
            isActive: settings.isActive ?? true,
          },
        });
      }

      return { success: true };
    } catch (error) {
      console.error("Error updating referral settings:", error);
      return { success: false, error: "Failed to update referral settings" };
    }
  }

  /**
   * Clear caches
   */
  clearCache(): void {
    this.referralCache.clear();
    this.couponCache.clear();
  }
}

export const referralService = new ReferralService();

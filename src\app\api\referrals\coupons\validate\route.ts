import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError, getCurrentUser } from "@/lib/api-utils";
import { referralService } from "@/lib/referral-service";
import { referralCouponValidationSchema } from "@/lib/validations";

// Validate referral coupon code
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Get current user
    const user = await getCurrentUser();
    if (!user) {
      return apiError("Authentication required", 401);
    }

    const body = await req.json();
    const parsed = referralCouponValidationSchema.safeParse(body);
    
    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { code, originalPrice } = parsed.data;

    const result = await referralService.validateReferralCoupon(code, user.id, originalPrice);

    if (!result.isValid) {
      return apiError(result.error || "Invalid referral coupon", 400);
    }

    return apiSuccess({
      coupon: result.coupon,
      discountAmount: result.discountAmount,
      finalPrice: result.finalPrice,
    });
  } catch (error) {
    console.error("Error validating referral coupon:", error);
    return apiError("Failed to validate referral coupon", 500);
  }
}

import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError, checkPermission, getCurrentUser } from "@/lib/api-utils";
import { referralService } from "@/lib/referral-service";
import { referralCreateSchema } from "@/lib/validations";

// Get user's referrals (Client only)
export async function GET(): Promise<NextResponse> {
  try {
    // Only clients can access their referrals
    const permissionError = await checkPermission(["CLIENT"]);
    if (permissionError) return permissionError;

    const user = await getCurrentUser();
    if (!user) {
      return apiError("Authentication required", 401);
    }

    const referrals = await referralService.getUserReferrals(user.id);
    return apiSuccess(referrals);
  } catch (error) {
    console.error("Error fetching referrals:", error);
    return apiError("Failed to fetch referrals", 500);
  }
}

// Create new referral link (Client only)
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Only clients can create referrals
    const permissionError = await checkPermission(["CLIENT"]);
    if (permissionError) return permissionError;

    const user = await getCurrentUser();
    if (!user) {
      return apiError("Authentication required", 401);
    }

    const body = await req.json();
    const parsed = referralCreateSchema.safeParse(body);
    
    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { referralCode } = parsed.data;

    const result = await referralService.createReferral(user.id, referralCode);

    if (!result.success) {
      return apiError(result.error || "Failed to create referral", 400);
    }

    return apiSuccess(result.referral, "Referral link created successfully");
  } catch (error) {
    console.error("Error creating referral:", error);
    return apiError("Failed to create referral", 500);
  }
}

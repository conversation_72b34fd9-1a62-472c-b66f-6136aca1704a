"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Copy, 
  Users, 
  Gift, 
  TrendingUp, 
  CheckCircle, 
  Clock,
  Info,
  Plus
} from "lucide-react";
import { toast } from "sonner";
import { ReferralData, ReferralCouponData } from "@/types/api";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface ReferralStats {
  totalReferrals: number;
  totalConversions: number;
  totalCoupons: number;
  conversionRate: number;
}

export function ReferralPageContent() {
  const [referrals, setReferrals] = useState<ReferralData[]>([]);
  const [coupons, setCoupons] = useState<ReferralCouponData[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [referralCode, setReferralCode] = useState("");
  const [stats, setStats] = useState<ReferralStats>({
    totalReferrals: 0,
    totalConversions: 0,
    totalCoupons: 0,
    conversionRate: 0,
  });

  // Get base URL from environment
  const baseUrl = process.env.NEXT_PUBLIC_NEXTAUTH_URL || process.env.NEXTAUTH_URL || "https://homeworkassylum.com";

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [referralsRes, couponsRes] = await Promise.all([
        fetch("/api/referrals"),
        fetch("/api/referrals/coupons"),
      ]);

      if (referralsRes.ok) {
        const referralsData = await referralsRes.json();
        setReferrals(referralsData.data || []);
        
        // Calculate stats
        const totalReferrals = referralsData.data?.length || 0;
        const totalConversions = referralsData.data?.reduce((acc: number, ref: ReferralData) => 
          acc + (ref.referralUsages?.filter(usage => usage.isConverted).length || 0), 0) || 0;
        const conversionRate = totalReferrals > 0 ? (totalConversions / totalReferrals) * 100 : 0;
        
        setStats(prev => ({
          ...prev,
          totalReferrals,
          totalConversions,
          conversionRate: Math.round(conversionRate * 100) / 100,
        }));
      }

      if (couponsRes.ok) {
        const couponsData = await couponsRes.json();
        setCoupons(couponsData.data || []);
        setStats(prev => ({
          ...prev,
          totalCoupons: couponsData.data?.length || 0,
        }));
      }
    } catch (error) {
      console.error("Error fetching referral data:", error);
      toast.error("Failed to load referral data");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateReferral = async () => {
    if (!referralCode.trim()) {
      toast.error("Please enter a referral code");
      return;
    }

    try {
      setCreating(true);
      const response = await fetch("/api/referrals", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ referralCode }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Referral link created successfully!");
        setReferralCode("");
        setIsCreateDialogOpen(false);
        fetchData(); // Refresh data
      } else {
        toast.error(data.message || "Failed to create referral link");
      }
    } catch (error) {
      console.error("Error creating referral:", error);
      toast.error("Failed to create referral link");
    } finally {
      setCreating(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy:", error);
      toast.error("Failed to copy to clipboard");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading referrals...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Referral Program</h1>
          <p className="text-muted-foreground">
            Invite friends and earn rewards for every successful referral
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Referral Link
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Referral Link</DialogTitle>
              <DialogDescription>
                Create a personalized referral link to share with friends. Use your name or a memorable identifier.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="referralCode">Referral Code</Label>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-sm text-muted-foreground whitespace-nowrap">
                    {baseUrl}/register?ref=
                  </span>
                  <Input
                    id="referralCode"
                    placeholder="your-name"
                    value={referralCode}
                    onChange={(e) => setReferralCode(e.target.value)}
                    className="flex-1"
                  />
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center mt-2 text-xs text-muted-foreground cursor-help">
                        <Info className="h-3 w-3 mr-1" />
                        What should I use as my referral code?
                      </div>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      <p>Use your name, nickname, or any memorable identifier. Only letters, numbers, hyphens, and underscores are allowed. Spaces will be converted to hyphens.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <DialogFooter>
              <Button
                onClick={handleCreateReferral}
                disabled={creating || !referralCode.trim()}
              >
                {creating ? "Creating..." : "Create Link"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Referrals</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalReferrals}</div>
            <p className="text-xs text-muted-foreground">
              Links you've created
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Successful Referrals</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalConversions}</div>
            <p className="text-xs text-muted-foreground">
              Friends who registered
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.conversionRate}%</div>
            <p className="text-xs text-muted-foreground">
              Success rate
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reward Coupons</CardTitle>
            <Gift className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCoupons}</div>
            <p className="text-xs text-muted-foreground">
              Coupons earned
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Referral Links */}
        <Card>
          <CardHeader>
            <CardTitle>Your Referral Links</CardTitle>
            <CardDescription>
              Share these links with friends to earn rewards
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {referrals.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No referral links created yet</p>
                <p className="text-sm">Create your first referral link to get started</p>
              </div>
            ) : (
              referrals.map((referral) => (
                <div key={referral.id} className="space-y-3 p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge variant={referral.isActive ? "default" : "secondary"}>
                        {referral.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <span className="font-medium">{referral.referralCode}</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(referral.referralUrl)}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                  </div>
                  <div className="text-sm text-muted-foreground break-all">
                    {referral.referralUrl}
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>
                      {referral.referralUsages?.filter(usage => usage.isConverted).length || 0} successful referrals
                    </span>
                    <span className="text-muted-foreground">
                      Created {new Date(referral.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  {referral.referralUsages && referral.referralUsages.length > 0 && (
                    <div className="space-y-2">
                      <Separator />
                      <div className="text-sm font-medium">Recent Activity:</div>
                      {referral.referralUsages.slice(0, 3).map((usage) => (
                        <div key={usage.id} className="flex items-center justify-between text-sm">
                          <div className="flex items-center space-x-2">
                            {usage.isConverted ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <Clock className="h-4 w-4 text-yellow-500" />
                            )}
                            <span>{usage.referredUser?.name || usage.referredUser?.email}</span>
                          </div>
                          <span className="text-muted-foreground">
                            {new Date(usage.usedAt).toLocaleDateString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* Reward Coupons */}
        <Card>
          <CardHeader>
            <CardTitle>Your Reward Coupons</CardTitle>
            <CardDescription>
              Coupons earned from successful referrals
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {coupons.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Gift className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No reward coupons yet</p>
                <p className="text-sm">Earn coupons when friends register using your links</p>
              </div>
            ) : (
              coupons.map((coupon) => (
                <div key={coupon.id} className="space-y-3 p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge variant={coupon.isActive ? "default" : "secondary"}>
                        {coupon.discountPercentage}% OFF
                      </Badge>
                      <span className="font-mono font-medium">{coupon.code}</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(coupon.code)}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>
                      {coupon.currentUses}/{coupon.maxUses} uses
                    </span>
                    <span className="text-muted-foreground">
                      {coupon.expiresAt 
                        ? `Expires ${new Date(coupon.expiresAt).toLocaleDateString()}`
                        : "No expiration"
                      }
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Earned from referring: {coupon.referredUser?.name || coupon.referredUser?.email}
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

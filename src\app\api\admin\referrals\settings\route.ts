import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError, checkPermission } from "@/lib/api-utils";
import { referralService } from "@/lib/referral-service";
import { referralSettingsUpdateSchema } from "@/lib/validations";

// Get referral settings (Admin only)
export async function GET(): Promise<NextResponse> {
  try {
    // Only admins can access referral settings
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const settings = await referralService.getReferralSettings();
    return apiSuccess(settings);
  } catch (error) {
    console.error("Error fetching referral settings:", error);
    return apiError("Failed to fetch referral settings", 500);
  }
}

// Update referral settings (Admin only)
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Only admins can update referral settings
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const body = await req.json();
    const parsed = referralSettingsUpdateSchema.safeParse(body);
    
    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const result = await referralService.updateReferralSettings(parsed.data);

    if (!result.success) {
      return apiError(result.error || "Failed to update referral settings", 500);
    }

    const updatedSettings = await referralService.getReferralSettings();
    return apiSuccess(updatedSettings, "Referral settings updated successfully");
  } catch (error) {
    console.error("Error updating referral settings:", error);
    return apiError("Failed to update referral settings", 500);
  }
}

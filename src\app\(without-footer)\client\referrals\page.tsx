"use client";

import { Suspense } from "react";
import { ReferralPageContent } from "@/components/dashboard/client/referral-page-content";

function LoadingFallback() {
  return (
    <div className="p-4 lg:p-6">
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading referrals...</p>
        </div>
      </div>
    </div>
  );
}

export default function ClientReferralsPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <ReferralPageContent />
    </Suspense>
  );
}

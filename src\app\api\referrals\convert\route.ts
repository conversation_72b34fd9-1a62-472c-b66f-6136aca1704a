import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError } from "@/lib/api-utils";
import { referralService } from "@/lib/referral-service";
import prisma from "@/lib/prisma";

// Convert referral when user completes email verification
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    const { userId, referralCode } = body;

    if (!userId || !referralCode) {
      return apiError("User ID and referral code are required", 400);
    }

    // Verify the user exists and is verified
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, emailVerified: true }
    });

    if (!user) {
      return apiError("User not found", 404);
    }

    if (!user.emailVerified) {
      return apiError("User email not verified", 400);
    }

    // Convert the referral
    const result = await referralService.convertReferral(referralCode, userId);

    if (!result.success) {
      return apiError(result.error || "Failed to convert referral", 400);
    }

    return apiSuccess({
      converted: true,
      coupon: result.coupon
    }, "Referral converted successfully");
  } catch (error) {
    console.error("Error converting referral:", error);
    return apiError("Failed to convert referral", 500);
  }
}

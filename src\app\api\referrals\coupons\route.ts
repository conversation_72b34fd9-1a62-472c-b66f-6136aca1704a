import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError, checkPermission, getCurrentUser } from "@/lib/api-utils";
import { referralService } from "@/lib/referral-service";

// Get user's referral coupons (Client only)
export async function GET(): Promise<NextResponse> {
  try {
    // Only clients can access their referral coupons
    const permissionError = await checkPermission(["CLIENT"]);
    if (permissionError) return permissionError;

    const user = await getCurrentUser();
    if (!user) {
      return apiError("Authentication required", 401);
    }

    const coupons = await referralService.getUserReferralCoupons(user.id);
    return apiSuccess(coupons);
  } catch (error) {
    console.error("Error fetching referral coupons:", error);
    return apiError("Failed to fetch referral coupons", 500);
  }
}

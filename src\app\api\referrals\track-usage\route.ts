import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError } from "@/lib/api-utils";
import { referralService } from "@/lib/referral-service";
import prisma from "@/lib/prisma";

// Track referral usage when someone registers with a referral code
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    const { referralCode, userEmail } = body;

    if (!referralCode || !userEmail) {
      return apiError("Referral code and user email are required", 400);
    }

    // Find the user by email
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      select: { id: true }
    });

    if (!user) {
      return apiError("User not found", 404);
    }

    // Track the referral usage
    const result = await referralService.trackReferralUsage(referralCode, user.id);

    if (!result.success) {
      return apiError(result.error || "Failed to track referral usage", 400);
    }

    return apiSuccess({ tracked: true }, "Referral usage tracked successfully");
  } catch (error) {
    console.error("Error tracking referral usage:", error);
    return apiError("Failed to track referral usage", 500);
  }
}
